/* Dashboard Responsive Utilities */
/* Additional responsive classes for better mobile experience */

/* Mobile-first responsive breakpoints */
:root {
  --mobile-max: 640px;
  --tablet-min: 641px;
  --tablet-max: 768px;
  --desktop-min: 1024px;
}

/* Container responsive adjustments */
.dashboard-responsive-wrapper {
  @apply w-full min-h-screen bg-[#f5f7fb];
}

@media (max-width: 640px) {
  .dashboard-responsive-wrapper {
    @apply px-2 py-4;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-responsive-wrapper {
    @apply px-4 py-6;
  }
}

@media (min-width: 1024px) {
  .dashboard-responsive-wrapper {
    @apply px-6 py-6;
  }
}

/* Mobile navigation and header adjustments */
@media (max-width: 640px) {
  .dashboard-header-mobile {
    @apply text-center mb-4;
  }
  
  .dashboard-header-mobile h1 {
    @apply text-xl font-bold;
  }
  
  .dashboard-header-mobile p {
    @apply text-sm text-gray-600;
  }
}

/* Card stacking for mobile */
@media (max-width: 640px) {
  .dashboard-mobile-stack > * {
    @apply w-full;
  }
  
  .dashboard-mobile-card {
    @apply mb-4 last:mb-0;
  }
}

/* Touch-friendly buttons and interactions */
@media (max-width: 768px) {
  .dashboard-touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  .dashboard-button-mobile {
    @apply px-4 py-3 text-base;
  }
}

/* Horizontal scrolling for tables on mobile */
@media (max-width: 768px) {
  .dashboard-table-scroll {
    @apply overflow-x-auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .dashboard-table-scroll table {
    @apply min-w-full;
  }
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .dashboard-text-responsive-sm {
    @apply text-xs;
  }
  
  .dashboard-text-responsive-base {
    @apply text-sm;
  }
  
  .dashboard-text-responsive-lg {
    @apply text-base;
  }
  
  .dashboard-text-responsive-xl {
    @apply text-lg;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-text-responsive-sm {
    @apply text-sm;
  }
  
  .dashboard-text-responsive-base {
    @apply text-base;
  }
  
  .dashboard-text-responsive-lg {
    @apply text-lg;
  }
  
  .dashboard-text-responsive-xl {
    @apply text-xl;
  }
}

@media (min-width: 1024px) {
  .dashboard-text-responsive-sm {
    @apply text-sm;
  }
  
  .dashboard-text-responsive-base {
    @apply text-base;
  }
  
  .dashboard-text-responsive-lg {
    @apply text-lg;
  }
  
  .dashboard-text-responsive-xl {
    @apply text-xl;
  }
}

/* Spacing adjustments */
@media (max-width: 640px) {
  .dashboard-spacing-mobile {
    @apply space-y-3;
  }
  
  .dashboard-gap-mobile {
    @apply gap-3;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-spacing-tablet {
    @apply space-y-4;
  }
  
  .dashboard-gap-tablet {
    @apply gap-4;
  }
}

@media (min-width: 1024px) {
  .dashboard-spacing-desktop {
    @apply space-y-6;
  }
  
  .dashboard-gap-desktop {
    @apply gap-6;
  }
}

/* Chart and visualization responsive adjustments */
@media (max-width: 640px) {
  .dashboard-chart-mobile {
    height: 80px;
  }
  
  .dashboard-chart-mobile .chart-bar {
    min-height: 15px;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-chart-tablet {
    height: 100px;
  }
  
  .dashboard-chart-tablet .chart-bar {
    min-height: 18px;
  }
}

@media (min-width: 1024px) {
  .dashboard-chart-desktop {
    height: 128px;
  }
  
  .dashboard-chart-desktop .chart-bar {
    min-height: 20px;
  }
}

/* Hide/show elements based on screen size */
.dashboard-mobile-only {
  @apply block;
}

.dashboard-tablet-only {
  @apply hidden;
}

.dashboard-desktop-only {
  @apply hidden;
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-mobile-only {
    @apply hidden;
  }
  
  .dashboard-tablet-only {
    @apply block;
  }
  
  .dashboard-desktop-only {
    @apply hidden;
  }
}

@media (min-width: 1024px) {
  .dashboard-mobile-only {
    @apply hidden;
  }
  
  .dashboard-tablet-only {
    @apply hidden;
  }
  
  .dashboard-desktop-only {
    @apply block;
  }
}

/* Preserve aspect ratios for images and charts */
.dashboard-aspect-preserve {
  @apply aspect-square;
}

@media (max-width: 640px) {
  .dashboard-aspect-preserve {
    @apply aspect-video;
  }
}

/* Ensure minimum touch targets */
@media (max-width: 768px) {
  .dashboard-interactive {
    @apply min-h-[44px] min-w-[44px];
  }
}
