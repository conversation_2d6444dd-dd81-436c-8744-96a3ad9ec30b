/* Assessment Table Component Styles */
.assessment-table {
  @apply bg-white flex flex-col;
  border-color: #eaecf0;
  height: 800px;
}

.assessment-table__header {
  @apply flex flex-row items-center justify-between flex-shrink-0;
}

.assessment-table__header-text {
  /* Container for title and description */
}

.assessment-table__title {
  @apply text-lg font-semibold;
  color: #1e1e1e;
}

.assessment-table__description {
  @apply text-xs mt-1;
  color: #64707d;
}

.assessment-table__header-actions {
  @apply flex gap-2;
}

.assessment-table__new-button {
  @apply text-white text-xs;
  background-color: #6475e9;
}

.assessment-table__new-button:hover {
  background-color: #5a6bd8;
}

.assessment-table__new-button-icon {
  @apply w-4 h-4 mr-2;
}

.assessment-table__content {
  @apply flex flex-col flex-1 min-h-0;
}

.assessment-table__table-container {
  @apply flex-1 overflow-auto;
}

.assessment-table__table-row {
  border-color: #eaecf0;
}

.assessment-table__table-head {
  @apply font-medium;
  color: #64707d;
}

.assessment-table__table-cell {
  color: #1e1e1e;
}

.assessment-table__table-cell--secondary {
  color: #64707d;
}

.assessment-table__badge {
  @apply bg-[#f3f3f3];
  color: #64707d;
}

.assessment-table__badge--success {
  @apply bg-green-100 text-green-800 border-green-200;
}

.assessment-table__badge--warning {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

.assessment-table__action-buttons {
  @apply flex gap-2;
}

.assessment-table__action-button {
  @apply h-8 w-8;
}

.assessment-table__action-icon {
  @apply w-4 h-4;
  color: #64707d;
}

.assessment-table__pagination {
  @apply flex items-center justify-between pt-4 border-t mt-4 flex-shrink-0;
  border-color: #eaecf0;
}

.assessment-table__pagination-left {
  @apply flex items-center gap-2;
}

.assessment-table__pagination-text {
  @apply text-sm;
  color: #64707d;
}

.assessment-table__pagination-select {
  @apply w-16 h-8;
}

.assessment-table__pagination-right {
  @apply flex gap-1;
}

.assessment-table__pagination-button {
  @apply w-8 h-8;
}

.assessment-table__pagination-button--active {
  @apply text-white;
  background-color: #6475e9;
}

.assessment-table__pagination-button--active:hover {
  background-color: #5a6bd8;
}

.assessment-table__pagination-button--inactive {
  color: #64707d;
}

/* Responsive adjustments for assessment table */
@media (max-width: 640px) {
  .assessment-table {
    height: auto;
    min-height: 400px;
  }

  .assessment-table__header {
    @apply flex-col items-start gap-3;
  }

  .assessment-table__title {
    @apply text-base;
  }

  .assessment-table__description {
    @apply text-xs;
  }

  .assessment-table__new-button {
    @apply w-full justify-center;
  }

  .assessment-table__table-container {
    @apply overflow-x-auto;
  }

  .assessment-table__table-head,
  .assessment-table__table-cell {
    @apply text-xs px-2 py-2;
  }

  .assessment-table__action-buttons {
    @apply flex-col gap-1;
  }

  .assessment-table__pagination {
    @apply flex-col gap-3;
  }

  .assessment-table__pagination-left {
    @apply justify-center;
  }

  .assessment-table__pagination-right {
    @apply justify-center;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .assessment-table {
    height: 600px;
  }

  .assessment-table__header {
    @apply flex-row;
  }

  .assessment-table__table-head,
  .assessment-table__table-cell {
    @apply text-sm px-3 py-2;
  }
}

/* Preserve desktop layout */
@media (min-width: 1024px) {
  .assessment-table {
    height: 800px;
  }

  .assessment-table__header {
    @apply flex-row;
  }

  .assessment-table__pagination {
    @apply flex-row;
  }
}
