/* VIAIS Card Component Styles */
.viais-card {
  border-color: #eaecf0;
  background-color: #6475e9;
}

.viais-card__content {
  @apply flex flex-col space-y-1 p-4;
}

.viais-card__header {
  @apply text-center;
}

.viais-card__title {
  @apply text-xl font-semibold text-left;
  color: #f1f1f1;
}

.viais-card__description {
  @apply text-xs text-left mb-4;
  color: #f1f1f1;
}

.viais-card__strengths-grid {
  @apply grid grid-cols-2 gap-2;
}

.viais-card__strength-item {
  @apply rounded-lg p-2;
  background-color: #f8f9fa;
}

.viais-card__strength-name {
  @apply text-xs font-medium truncate;
  color: #64707d;
}

.viais-card__strength-score {
  @apply text-sm font-semibold;
  color: #1e1e1e;
}

/* Responsive adjustments for VIAIS card */
@media (max-width: 640px) {
  .viais-card__content {
    @apply p-3;
  }

  .viais-card__title {
    @apply text-lg;
  }

  .viais-card__description {
    @apply text-xs mb-3;
  }

  .viais-card__strengths-grid {
    @apply grid grid-cols-1 gap-2;
  }

  .viais-card__strength-item {
    @apply p-2;
  }

  .viais-card__strength-name {
    @apply text-xs;
  }

  .viais-card__strength-score {
    @apply text-sm;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .viais-card__strengths-grid {
    @apply grid grid-cols-2 gap-2;
  }
}

/* Preserve desktop layout */
@media (min-width: 1024px) {
  .viais-card__content {
    @apply p-4;
  }

  .viais-card__title {
    @apply text-xl;
  }

  .viais-card__strengths-grid {
    @apply grid grid-cols-2 gap-2;
  }
}
